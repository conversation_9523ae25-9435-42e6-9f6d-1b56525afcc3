package com.example.gymbro.core.network.router

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import timber.log.Timber

/**
 * ConversationScope - 独立对话作用域
 *
 * 🔥 多轮对话架构升级核心组件：
 * - 为每个对话提供独立的协程作用域和数据流
 * - 支持token流和事件流的独立管理
 * - 提供完整的生命周期管理和资源清理
 * - 支持背压控制和性能监控
 *
 * 设计原则：
 * - 完全状态隔离：每个对话有独立的状态和数据流
 * - 资源安全：SupervisorJob确保异常隔离
 * - 性能监控：提供详细的性能指标
 * - 优雅关闭：支持资源的优雅释放
 */
class ConversationScope(
    val messageId: String,
    parentScope: CoroutineScope,
) : CoroutineScope {
    companion object {
        private const val TAG = "ConversationScope"

        // 🔥 缓冲区配置
        private const val TOKEN_BUFFER_CAPACITY = 256
        private const val EVENT_BUFFER_CAPACITY = 64
    }

    // 🔥 独立的协程作用域，使用SupervisorJob确保异常隔离
    private val supervisorJob = SupervisorJob(parentScope.coroutineContext[Job])
    override val coroutineContext = parentScope.coroutineContext + supervisorJob + CoroutineName("ConversationScope-$messageId")

    // 🔥 独立的token流
    private val _tokens = MutableSharedFlow<String>(
        replay = 0,
        extraBufferCapacity = TOKEN_BUFFER_CAPACITY,
        onBufferOverflow = kotlinx.coroutines.channels.BufferOverflow.SUSPEND,
    )
    val tokens: SharedFlow<String> = _tokens.asSharedFlow()

    // 🔥 独立的事件流
    private val _events = MutableSharedFlow<Any>(
        replay = 0,
        extraBufferCapacity = EVENT_BUFFER_CAPACITY,
        onBufferOverflow = kotlinx.coroutines.channels.BufferOverflow.SUSPEND,
    )
    val events: SharedFlow<Any> = _events.asSharedFlow()

    // 🔥 生命周期时间戳
    private val createdTime = System.currentTimeMillis()
    private var lastActivityTime = createdTime

    // 🔥 性能监控指标
    private var totalTokensEmitted = 0L
    private var totalEventsEmitted = 0L
    private var tokenEmissionFailures = 0L
    private var eventEmissionFailures = 0L

    init {
        Timber.tag(TAG).d("🔥 创建ConversationScope: messageId=$messageId")
    }

    /**
     * 发送token到token流
     *
     * @param token 要发送的token
     */
    suspend fun emitToken(token: String) {
        try {
            updateLastActivityTime()
            val success = _tokens.tryEmit(token)
            if (success) {
                totalTokensEmitted++
                Timber.tag(TAG).v("📡 Token发送成功: messageId=$messageId, token='${token.take(20)}...'")
            } else {
                // 如果tryEmit失败，使用emit（会挂起直到有空间）
                _tokens.emit(token)
                totalTokensEmitted++
                Timber.tag(TAG).v("📡 Token发送成功(挂起): messageId=$messageId, token='${token.take(20)}...'")
            }
        } catch (e: Exception) {
            tokenEmissionFailures++
            Timber.tag(TAG).e(e, "❌ Token发送失败: messageId=$messageId")
            throw e
        }
    }

    /**
     * 发送事件到事件流
     *
     * @param event 要发送的事件
     */
    suspend fun emitEvent(event: Any) {
        try {
            updateLastActivityTime()
            val success = _events.tryEmit(event)
            if (success) {
                totalEventsEmitted++
                Timber.tag(TAG).v("📡 Event发送成功: messageId=$messageId, event=${event::class.simpleName}")
            } else {
                // 如果tryEmit失败，使用emit（会挂起直到有空间）
                _events.emit(event)
                totalEventsEmitted++
                Timber.tag(TAG).v("📡 Event发送成功(挂起): messageId=$messageId, event=${event::class.simpleName}")
            }
        } catch (e: Exception) {
            eventEmissionFailures++
            Timber.tag(TAG).e(e, "❌ Event发送失败: messageId=$messageId")
            throw e
        }
    }

    /**
     * 检查scope是否活跃
     *
     * @return 是否活跃
     */
    fun isActive(): Boolean {
        return supervisorJob.isActive
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间戳
     */
    fun getCreatedTime(): Long {
        return createdTime
    }

    /**
     * 获取最后活动时间
     *
     * @return 最后活动时间戳
     */
    fun getLastActivityTime(): Long {
        return lastActivityTime
    }

    /**
     * 获取性能指标
     *
     * @return 性能指标对象
     */
    fun getMetrics(): ConversationScopeMetrics {
        return ConversationScopeMetrics(
            messageId = messageId,
            createdTime = createdTime,
            lastActivityTime = lastActivityTime,
            totalTokensEmitted = totalTokensEmitted,
            totalEventsEmitted = totalEventsEmitted,
            tokenEmissionFailures = tokenEmissionFailures,
            eventEmissionFailures = eventEmissionFailures,
            isActive = isActive(),
            tokenBufferUsage = getTokenBufferUsage(),
            eventBufferUsage = getEventBufferUsage(),
        )
    }

    /**
     * 获取token缓冲区使用情况
     *
     * @return 缓冲区使用百分比 (0.0 - 1.0)
     */
    private fun getTokenBufferUsage(): Float {
        // 由于SharedFlow的缓冲区状态不易直接获取，这里返回估算值
        // 实际项目中可以通过其他方式监控缓冲区状态
        return if (tokenEmissionFailures > 0) 0.8f else 0.1f
    }

    /**
     * 获取事件缓冲区使用情况
     *
     * @return 缓冲区使用百分比 (0.0 - 1.0)
     */
    private fun getEventBufferUsage(): Float {
        // 由于SharedFlow的缓冲区状态不易直接获取，这里返回估算值
        return if (eventEmissionFailures > 0) 0.8f else 0.1f
    }

    /**
     * 等待token订阅建立
     *
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否成功建立订阅
     */
    suspend fun waitForTokenSubscription(timeoutMs: Long): Boolean {
        return try {
            withTimeoutOrNull(timeoutMs) {
                // 简单的订阅检查：等待一小段时间后认为订阅已建立
                delay(100) // 等待100ms让订阅建立
                true
            } ?: false
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "等待token订阅超时: messageId=$messageId")
            false
        }
    }

    /**
     * 更新最后活动时间
     */
    private fun updateLastActivityTime() {
        lastActivityTime = System.currentTimeMillis()
    }

    /**
     * 关闭ConversationScope并释放所有资源
     */
    fun close() {
        Timber.tag(TAG).d("🗑️ 关闭ConversationScope: messageId=$messageId")

        try {
            // 取消所有协程
            supervisorJob.cancel("ConversationScope closed")

            // 记录最终指标
            val metrics = getMetrics()
            Timber.tag(TAG).d("📊 ConversationScope最终指标: $metrics")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 关闭ConversationScope时发生异常: messageId=$messageId")
        }
    }
}

/**
 * ConversationScope性能指标数据类
 */
data class ConversationScopeMetrics(
    val messageId: String,
    val createdTime: Long,
    val lastActivityTime: Long,
    val totalTokensEmitted: Long,
    val totalEventsEmitted: Long,
    val tokenEmissionFailures: Long,
    val eventEmissionFailures: Long,
    val isActive: Boolean,
    val tokenBufferUsage: Float,
    val eventBufferUsage: Float,
) {
    /**
     * 获取存活时间（毫秒）
     */
    val aliveTimeMs: Long
        get() = System.currentTimeMillis() - createdTime

    /**
     * 获取空闲时间（毫秒）
     */
    val idleTimeMs: Long
        get() = System.currentTimeMillis() - lastActivityTime

    /**
     * 检查是否接近缓冲区容量
     */
    val isNearCapacity: Boolean
        get() = tokenBufferUsage > 0.8f || eventBufferUsage > 0.8f

    override fun toString(): String {
        return "ConversationScopeMetrics(" +
            "messageId='$messageId', " +
            "aliveTime=${aliveTimeMs}ms, " +
            "idleTime=${idleTimeMs}ms, " +
            "tokens=$totalTokensEmitted, " +
            "events=$totalEventsEmitted, " +
            "tokenFailures=$tokenEmissionFailures, " +
            "eventFailures=$eventEmissionFailures, " +
            "active=$isActive, " +
            "tokenBuffer=${(tokenBufferUsage * 100).toInt()}%, " +
            "eventBuffer=${(eventBufferUsage * 100).toInt()}%" +
            ")"
    }
}
