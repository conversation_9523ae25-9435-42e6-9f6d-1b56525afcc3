package com.example.gymbro.features.thinkingbox

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import com.example.gymbro.features.thinkingbox.internal.presentation.ui.StreamingFinalRenderer

/**
 * ThinkingBox - The primary public API for the ThinkingBox feature module.
 * This Composable is self-contained and manages its own MVI lifecycle.
 * It is the sole entry point for integrating the ThinkingBox UI into other features.
 *
 * @param messageId The unique ID of the message being processed.
 * @param modifier Modifier for the Composable.
 * @param tokenizerService An optional service for token counting.
 * @param onDataReady A callback that is invoked when the thinking process is complete,
 *                    providing the final data for persistence by the calling feature.
 */
@Composable
fun ThinkingBox(
    messageId: String,
    modifier: Modifier = Modifier,
    tokenizerService: TokenizerService? = null,
    onDataReady: ((ThinkingBoxData) -> Unit)? = null,
) {
    // This public API function acts as a facade, calling the internal root Composable.
    ThinkingBoxRoot(
        messageId = messageId,
        modifier = modifier,
        tokenizerService = tokenizerService,
        onDataReady = onDataReady,
    )
}

/**
 * Data class representing the final, structured output of a ThinkingBox session.
 * This is the data packet provided in the `onDataReady` callback.
 */
data class ThinkingBoxData(
    val messageId: String,
    val finalMarkdown: String,
    val thinkingProcess: String, // JSON representation of thinking phases
    val duration: Long,
    val tokenCount: Int
)

/**
 * A static renderer for displaying final, non-streaming Markdown content.
 * This is typically used for rendering historical messages.
 *
 * @param finalMarkdown The complete Markdown string to render.
 * @param modifier Modifier for the Composable.
 */
@Composable
fun ThinkingBoxStaticRenderer(
    finalMarkdown: String,
    modifier: Modifier = Modifier,
) {
    if (finalMarkdown.isNotBlank()) {
        StreamingFinalRenderer(
            finalTokens = listOf(finalMarkdown),
            isFinalStreaming = false,
            onRenderingComplete = null,
            modifier = modifier
        )
    }
}


