package com.example.gymbro.features.thinkingbox.logging

import android.util.Log
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * ThinkingBox 专用日志树
 *
 * 实现日志瘦身，过滤低优先级的 token 级别日志
 */
class ThinkingBoxLogTree : Timber.DebugTree() {

    companion object {
        // ThinkingBox 相关的标签前缀
        private val THINKING_TAGS = setOf(
            "THINKINGBOX",
            "THINKINGBOX-RAW",
            "THINKINGBOX-MONITOR",
            "THINKINGBOX-HISTORY",
            "XML-PARSER",
            "ThinkingBoxFacade",
            "ThinkingMLGuardrail",
            "TB-RAW",
            "TB-FILTER",
            "TB-SEM",
            "TB-MAP",
            "TB-EVT",
            "TB-STATE",
            "TB-DB",
            "TB-UI",
            "TB-CONTENT", // 🔥 深度调试：perthink内容处理
            "PHASE-DEBUG", // 🔥 深度调试：业务层状态管理
            "FINAL-TYPEWRITER", // 🔥 Final标签打字机效果调试
            "SEAMLESS-RENDERER", // 🔥 无缝渲染器调试
            "AI-STREAM",
            "AI-RAW",
            // 🔥 【perthink调试】新增关键调试标签
            "TB-MAPPER", // DomainMapper事件处理
            "TB-REDUCER", // ThinkingReducer状态变化
            "TB-TITLE", // 标题处理事件
            "ThinkingBox-Debug", // UI状态调试
            "ThinkingHeader-Debug", // Header显示逻辑
            "TOKEN-FLOW", // Token处理流程
            "TB-MAIN", // 主要事件处理
            "RAW-TOKEN-COLLECTOR", // 🔥 【统一Token收集】RAW TOKEN收集器
            "RAW-TOKEN-RECORD", // 🔥 【RAW TOKEN记录】解析完成的token记录器
            // 🔥 【关键解析器标签】
            "TB-PARSER", // StreamingThinkingMLParser 核心解析器
            "RawChunkProcessor", // RawChunkProcessor 预处理器
            "ThinkingBox-VM", // ThinkingBoxViewModel
            "XmlStreamScanner", // XML扫描器
            "DomainMapper", // 领域映射器
            "ThinkingReducer", // 思考状态减速器
            // 🔥 新增：Coach 模块调试标签
            "REDUCER-DEBUG",
            "VIEWMODEL-DEBUG",
            "EFFECT-DEBUG",
            "USECASE-DEBUG",
            "ChatSessio...addMessage", // Repository 层日志标签
            "AiCoachViewModel",
            "MessagingReducerHandler",
            "SessionEffectHandler",
            "ChatSessionManagementUseCase",
            "ChatSessionRepositoryImpl",
            // 🔥 新增：Template保存调试标签
            "BUTTON-SAVE",
            "BUTTON-TEST",
            "UI-TEST",
            "UI-FEEDBACK",
            "TemplateEditReducer",
            "TemplateEditViewModel",
            "TemplateSaver",
            "TemplateScreen",
            "TemplateViewModel",
            "TemplateEffectHandler",
        )

        // 高频日志标签，需要特殊处理
        private val HIGH_FREQUENCY_TAGS = setOf(
            "THINKINGBOX-RAW",
            "XML-PARSER",
            "TB-RAW",
            "AI-STREAM",
            "AI-RAW",
        )
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 🔥 【调试模式】临时启用所有ThinkingBox相关日志用于问题诊断
        when {
            // 1. 🔥 【启用所有ThinkingBox日志】用于调试解析器问题
            isThinkingBoxTag(tag) -> {
                super.log(priority, tag, message, t)
                return
            }

            // 2. 🔥 【启用Token流日志】用于调试token处理问题
            tag?.contains("TOKEN-FLOW", ignoreCase = true) == true ||
            tag?.contains("RAW-TOKEN-COLLECTOR", ignoreCase = true) == true ||
            tag?.contains("ThinkingBox-VM", ignoreCase = true) == true -> {
                super.log(priority, tag, message, t)
                return
            }

            // 3. 🔥 【启用解析器日志】用于调试XML解析问题
            tag?.contains("XML-PARSER", ignoreCase = true) == true ||
            tag?.contains("StreamingThinkingMLParser", ignoreCase = true) == true ||
            tag?.contains("RawChunkProcessor", ignoreCase = true) == true -> {
                super.log(priority, tag, message, t)
                return
            }

            // 4. 🔥 【启用所有token相关日志】用于调试
            isTokenLevelLog(message) -> {
                super.log(priority, tag, message, t)
                return
            }

            // 5. 非 ThinkingBox 相关的日志，只显示 INFO 及以上
            !isThinkingBoxTag(tag) && priority < Log.INFO -> return
        }

        // 其他日志正常输出
        super.log(priority, tag, message, t)
    }

    /**
     * 检查是否为 ThinkingBox 相关标签
     */
    private fun isThinkingBoxTag(tag: String?): Boolean {
        if (tag == null) return false
        return THINKING_TAGS.any { thinkingTag ->
            tag.startsWith(thinkingTag, ignoreCase = true)
        }
    }

    /**
     * 检查是否为高频日志标签
     */
    private fun isHighFrequencyTag(tag: String?): Boolean {
        if (tag == null) return false
        return HIGH_FREQUENCY_TAGS.any { highFreqTag ->
            tag.startsWith(highFreqTag, ignoreCase = true)
        }
    }

    /**
     * 检查是否为 Token 级别的日志（需要过滤的token flow日志）
     */
    private fun isTokenLevelLog(message: String): Boolean {
        return message.contains("token", ignoreCase = true) ||
            message.contains("收到语义事件", ignoreCase = true) ||
            message.contains("发送内容", ignoreCase = true) ||
            message.contains("解析事件", ignoreCase = true) ||
            message.contains("TOKEN-FLOW", ignoreCase = true) ||
            message.contains("parseTokenStream", ignoreCase = true) ||
            message.contains("tokensSnapshot", ignoreCase = true) ||
            message.contains("处理Token", ignoreCase = true) ||
            message.contains("发送TextChunk", ignoreCase = true) ||
            message.contains("累积原始token", ignoreCase = true) ||
            message.contains("SemanticEvent", ignoreCase = true) ||
            message.contains("ThinkingEvent", ignoreCase = true) ||
            message.contains("映射并发射", ignoreCase = true) ||
            message.contains("事件处理", ignoreCase = true) ||
            message.contains("XML解析", ignoreCase = true) ||
            message.contains("标签处理", ignoreCase = true) ||
            message.contains("内容更新", ignoreCase = true) ||
            message.contains("流式处理", ignoreCase = true)
    }

    /**
     * 检查是否为 DEBUG 构建
     */
    private fun isDebugBuild(): Boolean {
        return try {
            // 这里可以根据实际的 BuildConfig 来判断
            // 暂时返回 true，在生产环境中应该返回 BuildConfig.DEBUG
            true
        } catch (e: Exception) {
            false
        }
    }

    override fun createStackElementTag(element: StackTraceElement): String? {
        // 为 ThinkingBox 相关的类添加特殊标签前缀
        val className = element.className
        return when {
            className.contains("thinkingbox", ignoreCase = true) -> {
                "THINKINGBOX-${super.createStackElementTag(element)}"
            }
            else -> super.createStackElementTag(element)
        }
    }
}

/**
 * ThinkingBox 日志配置工具
 */
object ThinkingBoxLogConfig {

    /**
     * 配置 ThinkingBox 日志系统 (v2.0 - 支持日志聚合)
     */
    fun configure() {
        // 移除默认的日志树
        Timber.uprootAll()

        // 植入 ThinkingBox 专用日志树
        Timber.plant(ThinkingBoxLogTree())

        // 🔥 修复递归调用：直接使用Android Log
        android.util.Log.i("ThinkingBoxLogConfig", "ThinkingBox 日志系统已配置 (支持TB-RAW/AI-STREAM聚合)")
    }

    /**
     * 强制刷新所有日志聚合器
     */
    fun flushAllAggregators() {
        LogAggregatorManager.cleanup()
        // 🔥 修复递归调用：直接使用Android Log
        android.util.Log.d("ThinkingBoxLogConfig", "所有日志聚合器已刷新")
    }

    /**
     * 启用详细日志（调试模式）
     */
    fun enableVerboseLogging() {
        Timber.uprootAll()
        Timber.plant(object : Timber.DebugTree() {
            override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                // 调试模式下显示所有日志
                super.log(priority, tag, message, t)
            }
        })

        android.util.Log.d("ThinkingBoxLogConfig", "ThinkingBox 详细日志已启用")
    }

    /**
     * 启用生产模式日志（只显示重要信息）
     */
    fun enableProductionLogging() {
        Timber.uprootAll()
        Timber.plant(object : Timber.DebugTree() {
            override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                // 生产模式只显示 INFO 及以上级别
                if (priority >= Log.INFO) {
                    super.log(priority, tag, message, t)
                }
            }
        })

        android.util.Log.i("ThinkingBoxLogConfig", "ThinkingBox 生产模式日志已启用")
    }

    /**
     * 获取日志统计信息
     */
    fun getLogStats(): LogStats {
        // 这里可以实现日志统计功能
        return LogStats(
            totalLogs = 0,
            debugLogs = 0,
            infoLogs = 0,
            warningLogs = 0,
            errorLogs = 0,
        )
    }
}

/**
 * 日志统计数据类
 */
data class LogStats(
    val totalLogs: Int,
    val debugLogs: Int,
    val infoLogs: Int,
    val warningLogs: Int,
    val errorLogs: Int,
) {
    override fun toString(): String {
        return "LogStats(total=$totalLogs, debug=$debugLogs, info=$infoLogs, warn=$warningLogs, error=$errorLogs)"
    }
}

/**
 * 日志聚合器 - 实现≥200 token才落一条日志的机制
 *
 * 🔥 核心功能：
 * - TB-RAW: ≥200 token 或 ≥1000ms 触发聚合输出
 * - AI-STREAM: ≥50条消息 或 ≥2000ms 触发聚合输出 (频率较低)
 * - AI-RAW: ≥100 token 或 ≥1500ms 触发聚合输出
 */
class LogAggregator(
    private val tag: String,
    private val tokenThreshold: Int = 200,
    private val timeThresholdMs: Long = 1000L,
    private val messageCountThreshold: Int = 50, // 新增：消息数量阈值
) {
    private val buffer = StringBuilder()
    private val tokenCount = AtomicLong(0)
    private val messageCount = AtomicLong(0) // 新增：消息计数器
    private var lastFlushTime = System.currentTimeMillis()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * 添加日志内容
     */
    fun append(message: String) {
        synchronized(buffer) {
            buffer.append(message).append(" ")

            // 简单token计数：按空格分词
            val tokens = message.split("\\s+".toRegex()).size
            val currentTokens = tokenCount.addAndGet(tokens.toLong())
            val currentMessages = messageCount.incrementAndGet()
            val currentTime = System.currentTimeMillis()

            // 检查是否需要刷新 - 支持多种触发条件
            if (currentTokens >= tokenThreshold ||
                currentMessages >= messageCountThreshold ||
                (currentTime - lastFlushTime) >= timeThresholdMs
            ) {
                flush()
            }
        }
    }

    /**
     * 强制刷新缓冲区
     */
    fun flush() {
        synchronized(buffer) {
            if (buffer.isNotEmpty()) {
                val content = buffer.toString().trim()
                val tokens = tokenCount.get()
                val messages = messageCount.get()

                // 🔥 修复递归调用：直接使用Android Log，避免通过Timber造成递归
                android.util.Log.i(
                    "$tag-AGGREGATED",
                    "🔍 [聚合] ${messages}条消息/${tokens}个token: ${content.take(
                        200,
                    )}${if (content.length > 200) "..." else ""}",
                )

                // 清空缓冲区
                buffer.clear()
                tokenCount.set(0)
                messageCount.set(0)
                lastFlushTime = System.currentTimeMillis()
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        flush()
        scope.cancel()
    }
}

/**
 * 日志聚合器管理器
 */
object LogAggregatorManager {
    private val aggregators = ConcurrentHashMap<String, LogAggregator>()

    /**
     * 获取或创建聚合器 - 根据标签类型使用不同配置
     */
    fun getAggregator(tag: String): LogAggregator {
        return aggregators.computeIfAbsent(tag) { createAggregatorForTag(it) }
    }

    /**
     * 根据标签类型创建合适的聚合器
     */
    private fun createAggregatorForTag(tag: String): LogAggregator {
        return when (tag) {
            "TB-RAW" -> LogAggregator(
                tag = tag,
                tokenThreshold = 200,
                timeThresholdMs = 1000L,
                messageCountThreshold = 100,
            )
            "AI-STREAM" -> LogAggregator(
                tag = tag,
                tokenThreshold = 100,
                timeThresholdMs = 2000L,
                messageCountThreshold = 20, // AI-STREAM频率较低，20条消息就聚合
            )
            "AI-RAW" -> LogAggregator(
                tag = tag,
                tokenThreshold = 150,
                timeThresholdMs = 1500L,
                messageCountThreshold = 50,
            )
            "THINKINGBOX-RAW" -> LogAggregator(
                tag = tag,
                tokenThreshold = 200,
                timeThresholdMs = 1000L,
                messageCountThreshold = 80,
            )
            "RAW-TOKEN-COLLECTOR" -> LogAggregator(
                tag = tag,
                tokenThreshold = 100, // 🔥 【100 TOKEN输出一次】
                timeThresholdMs = 2000L, // 2秒超时
                messageCountThreshold = 50,
            )
            else -> LogAggregator(
                tag = tag,
                tokenThreshold = 200,
                timeThresholdMs = 1000L,
                messageCountThreshold = 50,
            )
        }
    }

    /**
     * 清理所有聚合器
     */
    fun cleanup() {
        aggregators.values.forEach { it.cleanup() }
        aggregators.clear()
    }
}

/**
 * RAW TOKEN 记录器
 *
 * 🔥 【诊断工具】记录接收到的解析完成的token，100 TOKEN输出一次
 * 用于诊断token解析问题，仅在双时序被激活时触发
 */
object RawTokenRecorder {
    private val aggregator = LogAggregatorManager.getAggregator("RAW-TOKEN-COLLECTOR")
    private val tokenCount = AtomicLong(0)
    private var isActivated = false

    /**
     * 激活RAW TOKEN记录（仅在双时序激活时）
     */
    fun activate() {
        isActivated = true
        Timber.tag("RAW-TOKEN-COLLECTOR").e("🔥 [RAW TOKEN记录器] 已激活，100 TOKEN输出一次")
    }

    /**
     * 停用RAW TOKEN记录
     */
    fun deactivate() {
        isActivated = false
        aggregator.flush() // 强制输出剩余token
        Timber.tag("RAW-TOKEN-COLLECTOR").e("🔥 [RAW TOKEN记录器] 已停用")
    }

    /**
     * 记录解析完成的token
     */
    fun recordToken(token: String, source: String = "Unknown") {
        if (!isActivated) return

        val currentCount = tokenCount.incrementAndGet()
        val tokenInfo = "[$currentCount][$source] $token"

        // 使用聚合器记录，100 TOKEN输出一次
        aggregator.append(tokenInfo)

        // 每1000个token输出统计信息
        if (currentCount % 1000 == 0L) {
            Timber.tag("RAW-TOKEN-COLLECTOR").i("🔥 [统计] 已记录 $currentCount 个token")
        }
    }

    /**
     * 记录解析事件
     */
    fun recordEvent(eventType: String, content: String, source: String = "Parser") {
        if (!isActivated) return

        val eventInfo = "[EVENT][$source] $eventType: ${content.take(50)}${if (content.length > 50) "..." else ""}"
        aggregator.append(eventInfo)
    }

    /**
     * 强制输出当前缓冲的token
     */
    fun flush() {
        aggregator.flush()
    }

    /**
     * 获取当前token计数
     */
    fun getTokenCount(): Long = tokenCount.get()

    /**
     * 重置计数器
     */
    fun reset() {
        tokenCount.set(0)
        aggregator.flush()
        Timber.tag("RAW-TOKEN-COLLECTOR").d("🔥 [RAW TOKEN记录器] 计数器已重置")
    }
}
